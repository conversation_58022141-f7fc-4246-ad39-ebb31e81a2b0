import { STORE } from "@/constants/apiRoutes";
import { createServerApiService } from "@/services/serverApiService";

export interface IGeneralMetaTags {
  title?: string;
  pageName?: string;
  storeHandle?: string;
  isHome?: boolean;
}

export async function generalMetaTags({ title, isHome, pageName, storeHandle }: IGeneralMetaTags) {
  const serverApiService = createServerApiService(storeHandle);

  const store = await serverApiService
    .get(STORE)
    .then(res => res?.data?.data)
    .catch(() => null);

  console.log("store", store);
  console.log("STORE", storeHandle);

  const name = isHome ? title : `${pageName} | ${store?.name || "Shop Builder"}`;

  return {
    title: name || store?.name || "Shop Builder",
    description: store?.description || "Shop Builder",
    icons: {
      icon: store?.logo || "/icon-192x192.png",
      shortcut: store?.logo || "/icon-192x192.png",
      apple: store?.logo || "/icon-192x192.png"
    },
    themeColor: "#00359e",
    openGraph: {
      title: store?.name || "Shop Builder",
      description: store?.description || "Shop Builder",
      type: "website",
      images: [{ url: store?.logo, alt: store?.name }]
    },
    twitter: {
      card: "summary",
      title: store?.name || "Shop Builder",
      description: store?.description || "Shop Builder",
      images: [store?.logo]
    },
    appleWebApp: { capable: true }
  };
}
