import { ReactNode } from "react";
import { create } from "zustand";

type Options = {
  title?: ReactNode;
  closable?: boolean;
  fitHeight?: boolean;
  backdrop?: boolean;
  backdropBlur?: boolean;
  bottomClose?: boolean;
  events?: {
    onDidDismiss?: () => void;
    onBackdropTap?: () => void;
  };
};

type TUseActionSheetStore = {
  isOpen: boolean;
  title?: ReactNode;
  closable?: boolean;
  content: ReactNode | null;
  open: (content: ReactNode, paneOptions?: Options) => void;
  close: () => void;
};

// eslint-disable-next-line import/prefer-default-export
export const useActionSheet = create<TUseActionSheetStore>((set, get) => ({
  isOpen: false,
  title: undefined,
  closable: true,
  content: null,
  open: (content, paneOptions) => {
    if (get().isOpen) return;

    set(() => ({
      isOpen: true,
      content,
      title: paneOptions?.title,
      closable: paneOptions?.closable !== false
    }));
  },
  close: () => {
    set({ isOpen: false, content: null, title: undefined });
  }
}));
