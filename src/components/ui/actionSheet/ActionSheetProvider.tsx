"use client";

import React from "react";
import Close from "@/assets/icons/close.svg";
import { Drawer } from "vaul";
import { useActionSheet } from "./useActionSheetStore";

function ActionSheetProvider() {
  const { content, isOpen, close, title, closable } = useActionSheet();

  return (
    <Drawer.Root
      open={isOpen}
      onOpenChange={open => {
        if (!open) close();
      }}
    >
      <Drawer.Portal>
        <Drawer.Overlay
          className="fixed inset-0 mx-auto max-w-lg bg-black/60"
          style={{
            backdropFilter: "saturate(180%) blur(10px)"
          }}
        />
        <Drawer.Content className="fixed bottom-0 left-0 right-0 z-[999999999] mx-auto flex h-fit max-h-[85vh] max-w-lg flex-col rounded-t-[22px] bg-surface-primary outline-none">
          <div className="mx-auto mt-3 h-[5px] w-[66px] flex-shrink-0 rounded-full bg-gray-200" />
          {(title || closable) && (
            <div className="flex flex-shrink-0 items-center justify-between px-4 py-2">
              <span className="text-sm font-medium">{title}</span>
              {closable && <Close className="cursor-pointer" onClick={close} />}
            </div>
          )}
          <div className="min-h-0 flex-1 overflow-y-auto">{content}</div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}

export default ActionSheetProvider;
