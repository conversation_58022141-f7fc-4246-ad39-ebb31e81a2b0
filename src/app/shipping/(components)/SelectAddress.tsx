"use client";

import { useTranslations } from "next-intl";
import Location from "@/assets/icons/location.svg";
import ArrowLeft from "@/assets/icons/arrow-left.svg";
import { useProfileQuery } from "@/services/apis/profile/profile";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import ShippingInfo from "./shippingInfo";
import AddressForm from "./AddressForm";

function SelectAddress() {
  const t = useTranslations();
  const { close, open } = useActionSheet();

  const { data: profile } = useProfileQuery();

  const handleAddAddress = () => {
    close();
    setTimeout(() => {
      open(<AddressForm />, {
        title: t("shipping.addressForm.addTitle"),
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,
        events: {
          onDidDismiss: () => close(),
          onBackdropTap: () => close()
        }
      });
    }, 100);
  };

  return (
    <div className="p-4">
      <div className="flex flex-col border-t border-t-gray-40">
        <div
          className="flex cursor-pointer items-center justify-between border-b border-b-gray-40 py-4"
          onClick={handleAddAddress}
        >
          <div className="flex items-center gap-2">
            <Location />
            <span className="text-xs font-medium">{t("shipping.addNewAddress")}</span>
          </div>
          <ArrowLeft />
        </div>

        <div className="mt-3 flex flex-col gap-4">
          {profile?.data?.addresses?.map(item => (
            <ShippingInfo address={item} defaultAddress={profile?.data?.default_address} hasEdit />
          ))}
        </div>
      </div>
    </div>
  );
}

export default SelectAddress;
