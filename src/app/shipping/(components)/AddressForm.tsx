import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "@/components/ui/button";
import InputWrapper from "@/components/ui/inputWrapper";
import { useTranslations } from "next-intl";

import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { handleErrorResponse } from "@/utils/handleError";
import { useAddressMutation } from "@/services/apis/profile/profile";
import { IAddressBody } from "@/services/apis/profile/types";
import Input from "@/components/ui/input";
import LocationSelect from "@/components/ui/locationSelect/LocationsSelect";

import TextArea from "@/components/ui/textArea";

interface IAddressFormProps {
  id?: string;
  address?: string;
  contact_number?: string;
  first_name?: string;
  last_name?: string;
  location_id?: string;
  zip?: string;
  isEdit?: boolean;
}

function AddressForm({
  address,
  contact_number,
  first_name,
  last_name,
  location_id,
  zip,
  id,
  isEdit
}: IAddressFormProps) {
  const t = useTranslations();
  const { close } = useActionSheet();
  const { mutate: addressMutate, isPending: isLoadingAddAddress } = useAddressMutation();

  const isLoading = isLoadingAddAddress;

  const schema = yup.object().shape({
    address: yup.string().required(t("shipping.errors.required")),
    contact_number: yup.string(),
    first_name: yup.string().required(t("shipping.errors.required")),
    last_name: yup.string().required(t("shipping.errors.required")),
    location_id: yup.string().required(t("shipping.errors.required")),
    zip: yup.string().required(t("shipping.errors.required"))
  });

  const {
    control,
    handleSubmit,
    setError,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm({
    resolver: yupResolver(schema),
    mode: "all",
    defaultValues: {
      address: address || "",
      contact_number: contact_number || "",
      first_name: first_name || "",
      last_name: last_name || "",
      location_id: location_id || "",
      zip: zip || ""
    }
  });

  const onSubmit = async (values: IAddressBody) => {
    try {
      await addressMutate(
        {
          id: isEdit ? id : undefined,
          address: values?.address,
          contact_number: values?.contact_number,
          first_name: values?.first_name,
          last_name: values?.last_name,
          location_id: values?.location_id,
          zip: values?.zip
        },
        {
          onSuccess: () => {
            // queryClient?.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });
            close();
          },
          onError(error) {
            const bodyError = error?.response?.data;
            if (bodyError?.error_detail) {
              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  return (
    <div className="w-full gap-3 px-4 py-5">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-3.5">
          <Controller
            name="first_name"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-1">
                <span className="text-13 font-medium text-content-tertiary">{t("shipping.addressForm.firstName")}</span>
                <InputWrapper variant="filled" className="w-full" error={errors.first_name?.message}>
                  <Input
                    autoComplete="off"
                    value={field.value}
                    onBlur={field.onBlur}
                    onChange={field.onChange}
                    placeholder={t("shipping.addressForm.firstNamePlaceholder")}
                    aria-invalid={errors.first_name ? "true" : "false"}
                  />
                </InputWrapper>
              </div>
            )}
          />

          <Controller
            name="last_name"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-1">
                <span className="text-13 font-medium text-content-tertiary">{t("shipping.addressForm.lastName")}</span>
                <InputWrapper variant="filled" className="w-full" error={errors.last_name?.message}>
                  <Input
                    autoComplete="off"
                    value={field.value}
                    onBlur={field.onBlur}
                    onChange={field.onChange}
                    placeholder={t("shipping.addressForm.lastNamePlaceholder")}
                    aria-invalid={errors.last_name ? "true" : "false"}
                  />
                </InputWrapper>
              </div>
            )}
          />

          <Controller
            name="location_id"
            control={control}
            render={({ field }) => (
              <LocationSelect
                onSelect={val => setValue("location_id", val?.id)}
                value={field?.value}
                onBlur={field?.onBlur}
                placeholder={t("shipping.addressForm.stateAndCityPlaceholder")}
                label={t("shipping.addressForm.stateAndCity")}
                inputWrapperProps={{ variant: "filled", className: "w-full", error: errors.location_id?.message }}
              />
            )}
          />

          <Controller
            name="contact_number"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-1">
                <span className="text-13 font-medium text-content-tertiary">
                  {t("shipping.addressForm.contactNumder")}
                </span>

                <InputWrapper variant="filled" className="w-full" error={errors.contact_number?.message}>
                  <Input
                    autoComplete="off"
                    value={field.value}
                    onBlur={field.onBlur}
                    onChange={field.onChange}
                    placeholder={t("shipping.addressForm.contactNumderPlaceholder")}
                    aria-invalid={errors.contact_number ? "true" : "false"}
                  />
                </InputWrapper>
              </div>
            )}
          />

          <Controller
            name="zip"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-1">
                <span className="text-13 font-medium text-content-tertiary">{t("shipping.addressForm.zip")}</span>

                <InputWrapper variant="filled" className="w-full" error={errors.zip?.message}>
                  <Input
                    autoComplete="off"
                    value={field.value}
                    onBlur={field.onBlur}
                    onChange={field.onChange}
                    placeholder={t("shipping.addressForm.zipPlacehodler")}
                    aria-invalid={errors.contact_number ? "true" : "false"}
                  />
                </InputWrapper>
              </div>
            )}
          />

          <Controller
            name="address"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-1">
                <span className="text-13 font-medium text-content-tertiary">{t("shipping.addressForm.address")}</span>

                <InputWrapper variant="filled" className="w-full items-start" error={errors.address?.message}>
                  <TextArea
                    autoComplete="off"
                    className="h-[106px]"
                    value={field.value}
                    onBlur={field.onBlur}
                    onChange={field.onChange}
                    placeholder={t("shipping.addressForm.addressPlaceholder")}
                    aria-invalid={errors.contact_number ? "true" : "false"}
                  />
                </InputWrapper>
              </div>
            )}
          />

          <Button
            type="submit"
            className="mt-0.5 w-full"
            size="lg"
            disabled={isSubmitting || isLoading}
            isLoading={isLoading || isSubmitting}
          >
            {isEdit ? t("shipping.addressForm.editAddress") : t("shipping.addressForm.addressRegistration")}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddressForm;
