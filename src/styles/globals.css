@tailwind base;
@tailwind components;
@tailwind utilities;
@import "colors.css";
@import "swiper/css";
@import "swiper/css/navigation";
@import "swiper/css/pagination";
@import "react-toastify/dist/ReactToastify.css";
@import "toastify.css";

html {
  scroll-behavior: smooth;
  /* make our web feels like app by disallowing select element*/
  /* if you want to user be able to select text, you can specify it with css in that situation and that will override this*/
  user-select: none;
  /* for disable zoom on mobile (ios) */
  touch-action: pan-y;
  height: 100%;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

body {
  background: #dfdfdf;
  height: 100%;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

html::-webkit-scrollbar,
body::-webkit-scrollbar,
*::-webkit-scrollbar {
  display: none;
}

* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .ltr {
    direction: ltr;
  }
  .rtl {
    direction: rtl;
  }
  .dir-auto {
    direction: auto;
  }
  .dir-inherit {
    direction: inherit;
  }
  /* This adds extra bottom padding on ios mobile browsers */
  .ios-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.highcharts-axis-labels {
  @apply font-iran-yekan text-xs;
}
