{"name": "shop-builder-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "yarn lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "check-types": "tsc --pretty", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.59.15", "@types/node": "^22.5.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "axios": "^1.7.7", "downshift": "^9.0.9", "eslint": "^8", "eslint-config-next": "14.2.15", "framer-motion": "^12.6.3", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "next": "14.2.15", "next-intl": "^3.21.1", "nuqs": "^2.0.4", "query-string": "^9.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-intersection-observer": "^9.13.1", "react-otp-input": "^3.1.1", "react-toastify": "^11.0.3", "react-use": "^17.6.0", "sass": "^1.78.0", "swiper": "^11.1.12", "tailwind-merge": "^2.5.3", "typescript": "^5", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "yup": "^1.6.1", "zustand": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-onboarding": "^8.3.5", "@storybook/blocks": "^8.3.5", "@storybook/manager-api": "^8.3.5", "@storybook/nextjs": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@typescript-eslint/eslint-plugin": "^7", "@typescript-eslint/parser": "^7", "autoprefixer": "^10.4.20", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react": "^7.35.2", "eslint-plugin-storybook": "^0.9.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8.4.45", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "storybook": "^8.3.5", "tailwindcss": "^3.4.10", "validate-branch-name": "^1.3.1"}, "validate-branch-name": {"pattern": "^(master|main|develop){1}$|^(build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test)/.+$", "errorMsg": "Please follow standard branch name, rename branch using: git branch -m <oldname> <newname>"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}